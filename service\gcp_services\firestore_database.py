import datetime
from pathlib import Path
from typing import List, Optional, Union
from service.context_pipeline import logger
import firebase_admin
from firebase_admin import credentials, firestore
from pydantic import BaseModel, Field, ValidationError


class Database:
    """
    Helper around the Firestore collection `FMS_Pipeline`.
    """

    class AgentDatabaseData(BaseModel):
        agent_id: str = Field(..., description="Identifier for the agent being executed.")
        system_prompt: str = Field(..., description="System-level prompt for initializing the agent.")
        user_prompt: str = Field(..., description="User-specific prompt or instruction.")
        created_at: datetime.datetime = Field(
            default_factory=datetime.datetime.utcnow,
            description="Timestamp of agent context creation (UTC).",
        )
        usage: int = Field(default=0, description="Number of times the agent has been used.")
        privacy: str = Field(
            default="private",
            description="Privacy setting for the agent ('private', 'public', etc.).",
        )
        access_ids: List[str] = Field(
            default_factory=list, description="List of user or group IDs allowed access."
        )
        ratings: List[float] = Field(
            default_factory=list, description="List of ratings provided for the agent."
        )
        tags: List[str] = Field(
            default_factory=list, description="Tags associated with the agent."
        )
        last_used: Optional[datetime.datetime] = Field(
            default=None, description="Timestamp when last used."
        )
        is_active: bool = Field(default=True, description="Whether the agent is currently active.")
        is_approved: bool = Field(
            default=False, description="Whether the agent is approved for general use."
        )


    def __init__(self, config_path: Union[str, Path] = "database_config.json"):
        """
        Initialise a Firestore client from a Firebase *service-account* JSON file.
        """
        config_path = Path(config_path).expanduser().resolve()
        if not config_path.exists():
            raise FileNotFoundError(f"Cannot locate {config_path}")

        try:
            self._app = firebase_admin.get_app()
        except ValueError:
            cred = credentials.Certificate(str(config_path))
            self._app = firebase_admin.initialize_app(cred)

        self._db = firestore.client()
        self._collection = self._db.collection("FMS_Pipeline")


    def save_agent(self, agent_id: str, remaining_data: dict) -> str:
        """
        Upsert `FMS_Pipeline/{agent_id}` with validated data.

        Any keys that are **not** listed in the AgentDatabaseData schema are ignored.
        Keys with "empty" values (None, "", [], {}) are also ignored so that
        the corresponding fields fall back to their schema defaults.
        """
        if not isinstance(remaining_data, dict):
            raise TypeError("remaining_data must be a dict")

        valid_fields = self.AgentDatabaseData.__fields__.keys()
        clean_data = {
            k: v
            for k, v in remaining_data.items()
            if k in valid_fields and v not in (None, "", [], {})
        }

        try:
            payload = self.AgentDatabaseData(agent_id=agent_id, **clean_data).dict(exclude_none=True)
        except ValidationError as exc:
            raise exc

        for k, v in payload.items():
            if isinstance(v, datetime.datetime) and v.tzinfo is None:
                payload[k] = v.replace(tzinfo=datetime.timezone.utc)

        self._collection.document(agent_id).set(payload)
        return agent_id


    def load_agent(self, agent_id: str) -> dict:
        """
        Retrieve `FMS_Pipeline/{agent_id}` and return it as a dict.
        Raises KeyError if the document does not exist.
        """
        logger.info(f"Loading agent {agent_id} from FMS_Pipeline.")
        doc = self._collection.document(agent_id).get()
        if not doc.exists:
            logger.warning(f"Agent '{agent_id}' not found in FMS_Pipeline.")
            return {}
        
        return doc.to_dict()