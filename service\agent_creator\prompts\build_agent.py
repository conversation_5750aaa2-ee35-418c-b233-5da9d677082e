def build_agent(system_meta, user_meta, data, client, openai_model="gpt-4.1"):
    system_prompt = openai_text_call(client, system_meta, model=openai_model)
    user_prompt = openai_text_call(client, user_meta, model=openai_model)
    
    agent = {
        "name": data["name"],
        "description": data["description"],
        "input_params_dict": data["input_params_dict"],
        "output_desc": data["output_desc"],
        "system_prompt": system_prompt,
        "user_prompt": user_prompt,
        "owner": data["owner"],
    } 
    return agent