# Generate Assignment endpoint setup
# Handles incoming requests and parses request parameters
# Generates the assignment and returns the response
import os
from uuid import uuid4
#from service.ela.ela_graph import reading_comp_gen_flow
import time
start_time = time.time()
from service.context_pipeline import logger

# from service.copilot_flow_manager import route_tool
from service.copilot_pre_processor import preprocess_copilot_input
from service.agent_creator.create_agent_pipeline import create_agent_pipeline
def FMS_pipeline(request):
    try:
        global logger
        logger.info(f"Starting generate_assignment with request:{request.json}")

        request_id = request.headers.get("X-Cloud-Trace-Context", str(uuid4()))
        if '/' in request_id:
            request_id = request_id.split('/')[0]
        else:
            logger.info(f"Request ID format different{request_id}")
        logger.info(f"request_id: {request_id}")
        
        allowed_origins = ['https://uniqlearn.web.app']
        origin = request.headers.get('Origin')
        
        logger.info(f'Beginning of generate_assignment:{request}')

        headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization',
            'Access-Control-Max-Age': '3600'
        }


        if request.method == 'OPTIONS':
            return ('', 204, headers)

        data = request.json
        # tool_type = data.get("tool_type", "lesson_plan")
        # preprocessors = {
        #     "lesson_plan": preprocess_lesson_plan_input,
        #     "professional_email": preprocess_professional_email_input,
        #     "rubric_generator": preprocess_rubrics_input,
        #     "worksheet_generator": preprocess_worksheet_input,
        #     "basic_chatbot": preprocess_basic_chatbot_input
        #     # add other tools here as you expand
        # }
        ptx=preprocess_copilot_input(data, request_id)
        logger.info(f"Preprocessed input: {ptx}")
        create_agent_pipeline(ptx)
        return ""
        # try:
        #     Ptx = preprocess_func(data, request_id,default_ctx)
        #     logger.info(f"Preprocessed input: {Ptx}")
        # except Exception as e:
        #     logger.error(f"Error in preprocess_gen_input: {e}")
            # try: 
            #     aisdk_object.slack_logging.log(Ptx,start_time,error_flag=True)
            # except Exception as e:
            #     logger.error(f"Error in slack_logging: {e}")
            # raise
        # try:
        #     response = route_tool(Ptx, langfuse_handler)
        #     logger.info(f"Response from api: {response}")
        #     return (response, 200, headers)
        # except Exception as e:
        #     # try: 
        #     #     aisdk_object.slack_logging.log(Ptx,start_time,error_flag=True)
        #     # except Exception as e:
        #     #     logger.error(f"Error in slack_logging: {e}")
        #     if hasattr(Ptx, 'logging_fields'):
        #         logger.error(f"Error in generating flow: {e}")
        #     else:
        #         logger.error(f"Error in generating flow: {e}")
        #     raise
    
    except Exception as e:
        logger.error(f"Error in generate_assignment: {e}")
        return (
            f"Error processing request: {request_id}",
            500, 
            headers)
