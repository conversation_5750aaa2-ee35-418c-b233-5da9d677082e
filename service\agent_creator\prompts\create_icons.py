from service.openai_utils.gpt_call_new import openai_json_call
import json
def build_icon_prompts_gpt(tool_name, tool_desc, selected_idea):
    system_prompt = (
        "You are an expert at writing prompts for AI image generation (like DALL-E) to create app icons for a next-generation educational SaaS product (UniqLearn). "
        "All icons you prompt for must use the exact same background: "
        "**A white or very light off-white background with a soft, subtle horizontal pastel gradient (using pink, purple, blue, yellow, and green in airy, gentle tones), with a mild glassmorphism (frosted/glassy effect) and a soft shadow for separation.**\n"
        "Do not alter this background in any icon. "
        "For the icon itself, use:\n"
        "1. Clean, rounded shapes (no sharp corners)\n"
        "2. Unified visual style (playful 3D or 2.5D blended with flat glyphs)\n"
        "3. Orange for action highlights, blue for notification/clarity, and gray for details\n"
        "4. No text, no faces, no dark or saturated colors, no clutter, and never childish or overly formal/corporate\n"
        "Icons must look modern, friendly, simple, and perfect for K-12 teacher tools in a 2024-2025 SaaS product."
    )

    user_prompt = (
        f"Tool name: {tool_name}\n"
        f"Description: {tool_desc}\n"
        f"Selected icon idea: {selected_idea['visual_description']}\n"
        "Write a DALL-E prompt (positive_prompt) for generating a single app icon in this style, ensuring the background is always the same as described above. "
        "Also write a negative_prompt listing what should be avoided. "
        "Return JSON with keys: 'positive_prompt', 'negative_prompt'."
    )
    prompts = openai_json_call(system_prompt, user_prompt)
    return prompts

def select_best_idea_gpt(tool_name, tool_desc, ideas):
    system_prompt = (
        "You are an expert in icon design for educational software."
        "You are given three possible icon ideas for a tool."
    )
    user_prompt = (
        f"Tool name: {tool_name}\nDescription: {tool_desc}\n"
        f"Here are the icon ideas: {json.dumps(ideas)}\n"
        "Select the idea that would make the most effective and intuitive app icon for this tool. "
        "Return your answer in JSON with keys: 'selected_title', 'reason', 'visual_description'."
    )
    selection = openai_json_call(system_prompt, user_prompt)
    return selection

def brainstorm_icon_ideas_gpt(tool_name, tool_desc):
    system_prompt = (
        "You are a creative AI assistant for UI designers working on a modern educational SaaS platform. "
        "All icons should be inspired by the UniqLearn dashboard style: "
        "• Clean, rounded fonts and UI elements\n"
        "• Light glassmorphism and blurred backgrounds\n"
        "• Friendly, unified iconography—mixing playful 3D figures and flat glyphs\n"
        "• A color palette of bright white, soft off-white, and pastel gradients (pink, purple, blue, yellow, green)\n"
        "• High-attention orange for key actions and blue for clarity/accent\n"
        "• Accents in light gray for secondary elements\n"
        "• Overall, icons should feel bright, friendly, modern, and approachable for teachers and K-12—never childish, always professional yet welcoming.\n"
        "Keep the focus on clarity, simplicity, and a data-friendly, uncluttered look."
    )
    user_prompt = (
        f"Given this tool's name and description, list 3 creative ideas for how its concept could be represented as a simple, modern app icon that matches the above guidelines. "
        f"Each idea should include a visual description, a short icon title, and a sentence explaining the concept. "
        f"Use visual motifs or shapes that would feel at home on a bright, pastel-gradient card with subtle shadows and soft edges. "
        f"Respond in JSON as a list with fields: 'title', 'visual_description', 'concept_explanation'.\n\n"
        f"Tool name: {tool_name}\nDescription: {tool_desc}"
    )
    ideas = openai_json_call(system_prompt, user_prompt)
    return ideas