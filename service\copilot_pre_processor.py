from service.copilot_datamodels import *
from service.gcp_services.firestore_database import Database
from service.context_pipeline import logger
def preprocess_copilot_input(input_data,request_id):
    logger.info(f"Preprocessing copilot input for request id {request_id}")
    db = Database("./staging_config.json")
    # try:
    #     agent_data=db.load_agent(input_data["agent_id"])
    #     logger.info(f"Agent data loaded: {agent_data}")
    # except:
    #     agent_data=None
    # if agent_data:
    #     request_data=RunAgentRequest(agent_id=input_data["agent_id"],users_query=input_data["users_query"])
    # else:
    default={}
    default['agent_request']=RegisterAgentRequest(**input_data)
    ptx=AgentContextPipeline(**default)

    print(f"Preprocessed copilot data: {ptx}")

    return ptx
    