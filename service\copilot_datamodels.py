from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional, Any, Union
from datetime import datetime



class RunAgentRequest(BaseModel):
    # The minimal payload if the agent already exists
    agent_id: str = Field(..., description="ID of an existing agent")
    user_request: str = Field(..., description="What the user wants the agent to do")

class RegisterAgentRequest(BaseModel):
    # Everything that’s needed when the agent doesn’t exist yet
    agent_id: str = Field(..., description="Unique identifier for the agent")
    name: str = Field(..., description="Human-readable name of the agent")
    description: str = Field(..., description="Detailed description of the agent")
    input_params_dict: Dict[str, str] = Field(
        default_factory=dict, description="input name → type"
    )
    output_desc: str = Field(..., description="Expected output description")
    owner: str = Field(..., description="Owner of the agent")

# FastAPI (or any other consumer) can now accept either payload:
AgentRequest = Union[RunAgentRequest, RegisterAgentRequest]
class AgentContextPipeline(BaseModel):
    """
    Represents the context and configuration for an agent in the pipeline.
    """
    agent_request: AgentRequest = Field(..., description="The request object containing agent configuration.")
    system_prompt: str = Field(default="", description="System-level prompt for initializing the agent.")
    user_prompt: str = Field(default="", description="User-specific prompt or instruction.")
    icon_path: Optional[str] = Field(None, description="Optional path or URL to the agent's icon.")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Timestamp of agent context creation (UTC).")
    usage: int = Field(default=0, description="Number of times the agent has been used.")
    privacy: str = Field(default="private", description="Privacy setting for the agent ('private', 'public', etc.).")
    access_ids: List[str] = Field(default_factory=list, description="List of user or group IDs allowed access.")
    ratings: List[float] = Field(default_factory=list, description="List of ratings provided for the agent.")
    tags: List[str] = Field(default_factory=list, description="List of tags associated with the agent for search/filtering.")
    last_used: Optional[datetime] = Field(default=None, description="Timestamp when the agent was last used.")
    is_active: bool = Field(default=True, description="Indicates whether the agent is currently active.")
    is_approved: bool = Field(default=False, description="Indicates whether the agent is approved for general use.")
    env: Optional[str] = Field(default=None, description="Environment in which the agent operates (e.g., 'production', 'staging').")

class AgentRun(BaseModel):
    """
    Represents the execution/run of an agent for a given request.
    """
    run_id: str = Field(..., description="Unique identifier for this agent run/execution.")
    agent_id: str = Field(..., description="Identifier for the agent being executed.")
    user_id: Optional[str] = Field(default=None, description="Optional ID of the user who initiated the run.")
    input_values: Dict[str, Any] = Field(default_factory=dict, description="Actual input values provided to the agent in this run.")
    run_time: datetime = Field(default_factory=datetime.utcnow, description="Timestamp when the agent run was started (UTC).")
    output: Optional[Any] = Field(default=None, description="Output/result produced by the agent during this run.")
    status: str = Field(default="success", description="Status of the agent run (e.g., 'success', 'failed', 'running').")
    error_message: Optional[str] = Field(default=None, description="Error message if the run failed; otherwise None.")

class Database(BaseModel):
    agent_id: str = Field(..., description="Identifier for the agent being executed.")
    system_prompt: str = Field(..., description="System-level prompt for initializing the agent.")
    user_prompt: str = Field(..., description="User-specific prompt or instruction.")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Timestamp of agent context creation (UTC).")
    usage: int = Field(default=0, description="Number of times the agent has been used.")
    privacy: str = Field(default="private", description="Privacy setting for the agent ('private', 'public', etc.).")
    access_ids: List[str] = Field(default_factory=list, description="List of user or group IDs allowed access.")
    ratings: List[float] = Field(default_factory=list, description="List of ratings provided for the agent.")
    tags: List[str] = Field(default_factory=list, description="List of tags associated with the agent for search/filtering.")
    last_used: Optional[datetime] = Field(default=None, description="Timestamp when the agent was last used.")
    is_active: bool = Field(default=True, description="Indicates whether the agent is currently active.")
    is_approved: bool = Field(default=False, description="Indicates whether the agent is approved for general use.")