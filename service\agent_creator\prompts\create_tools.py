
def generate_tool_icon(agent_params, client, out_dir="agent_icons"):
    os.makedirs(out_dir, exist_ok=True)
    tool_name = agent_params.get("name", "Tool")
    tool_desc = agent_params.get("description", "")

    # Step 1: Brainstorm 3 icon ideas with GPT
    ideas = brainstorm_icon_ideas_gpt(client, tool_name, tool_desc)
    
    # Step 2: Select the best idea using GPT
    selection = select_best_idea_gpt(client, tool_name, tool_desc, ideas)
    
    # Step 3: Build prompts using GPT
    prompts = build_icon_prompts_gpt(client, tool_name, tool_desc, selection)
    positive_prompt = prompts['positive_prompt']
    negative_prompt = prompts['negative_prompt']
    
    # Step 4: Generate icon using OpenAI image API (DALL-E 3 or similar)
    image_response = client.images.generate(
        model="dall-e-3",
        prompt=f"Dont do this:{negative_prompt}\n INSTEAD DO THIS:{positive_prompt}",
        n=1,
        size="1024x1024"
    )
    image_url = image_response.data[0].url
    # Download and save the image
    file_name = f"{tool_name.lower().replace(' ', '_')}_icon.png"
    file_path = os.path.abspath(os.path.join(out_dir, file_name))
    img_data = requests.get(image_url).content
    with open(file_path, "wb") as handler:
        handler.write(img_data)

    return file_path, selection, ideas, prompts