from openai import OpenAI
import json

client = OpenAI(api_key="***************************************************")

def openai_json_call(system_prompt, user_prompt, model="gpt-4.1"):
    """
    Calls OpenAI with a system and user prompt, expects JSON response.

    Args:
        client (OpenAI): OpenAI client instance.
        system_prompt (str): System message content.
        user_prompt (str): User message content.
        model (str): OpenAI model name.

    Returns:
        dict or str: Parsed JSON if possible, else raw content.
    """
    response = client.chat.completions.create(
        model=model,
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        response_format={"type": "json_object"}
    )
    content = response.choices[0].message.content

    try:
        return json.loads(content)
    except Exception:
        return content
    

def openai_text_call(prompt, model="gpt-4.1"):
    response = client.chat.completions.create(
        model=model,
        messages=[{"role": "user", "content": prompt}]
    )
    return response.choices[0].message.content.strip()

def openai_image_call(positive_prompt, negative_prompt):
    image_response = client.images.generate(
        model="dall-e-3",
        prompt=f"Dont do this:{negative_prompt}\n INSTEAD DO THIS:{positive_prompt}",
        n=1,
        size="1024x1024",
        response_format="b64_json"
    )
    base64_image = image_response.data[0].b64_json
    return base64_image