from service.openai_utils.gpt_call_new import openai_text_call

def create_agent(name: str, description: str, input_params_dict: dict, output_desc: str) -> dict:
    """
    This function is a basic chatbot agent that uses GPT to generate a response to a user query.
    It takes in a user query and a conversation history, and returns a response.
    The response is generated by GPT, and is returned as a dictionary with the key "response".
    """
    input_param_str = "\n".join([f"  - {k}: {v}" for k, v in input_params_dict.items()])

    system_prompt = f"""You are an expert AI prompt engineer.

    Your task is to write a **system prompt** for an autonomous AI agent (GPT-4.1) that will act as a digital assistant for teachers, supporting them in the classroom.

    Tool Name: {name}
    Description: {description}
    Input Parameters:
    {input_param_str}
    Output: {output_desc}

    **Instructions:**
    - Write a clear, concise, and comprehensive system prompt that guides GPT-4.1 to act as a helpful, reliable assistant for teachers, following the tool description above.
    - Define the agent’s role and persona as a classroom-appropriate assistant supporting teachers in their daily work (lesson delivery, engagement, class management, etc.).
    - Ensure the agent understands its responses are for teachers, and should support their effectiveness and classroom environment.
    - Include necessary educational context for successful, teacher-friendly task completion.
    - Specify constraints (e.g., output must be safe, inclusive, positive, and appropriate for school settings).
    - Make instructions actionable, unambiguous, and robust against misuse.
    - Add examples or templates for output if helpful.
    - Ensure the prompt covers edge cases, supports differentiation (various grade levels, subjects), and enhances teaching impact.

    **Output Format:**
    Return only the final system prompt as plain text, without any commentary or markdown.

    Begin.
    """
    user_prompt = f"""You are an expert AI prompt engineer.

    Your task is to write a **user-facing prompt** for an autonomous AI teacher assistant agent (GPT-4.1) based on the following tool specification:

    Tool Name: {name}
    Description: {description}
    Input Parameters:
    {input_param_str}
    Output: {output_desc}

    **Instructions:**
    - Write a clear and supportive prompt that will be presented to teachers using this assistant agent.
    - Clearly ask the teacher to provide all required input parameters, using natural, friendly, and professional language.
    - Briefly explain how this tool will benefit the teacher’s classroom practice or save them time.
    - If helpful, provide a short example of how to enter inputs.
    - Make the prompt concise, positive, and accessible to teachers of any tech skill level.
    - Avoid jargon and keep instructions classroom-focused.

    **Output Format:**
    Return only the user prompt as plain text, without any commentary or markdown.

    Begin.
    """

    generated_system_prompt=openai_text_call(system_prompt)
    generated_user_prompt=openai_text_call(user_prompt)

        
    return (generated_system_prompt,generated_user_prompt)
