# from service.context_pipeline import logger
# from service.tools.graphs.lesson_plan import lesson_plan_graph
# from service.tools.graphs.professional_email import professional_email_graph
# from service.tools.graphs.rubric_generator import rubric_generator_langgraph
# # from service.tools.graphs.professional_email import professional_email_langgraph
# # from service.tools.graphs.text_proofreader import text_proofreader_langgraph
# from service.tools.graphs.worksheet_generator import worksheet_generator_langgraph
# from service.tools.graphs.basic_chatbot import basic_chatbot_langgraph

# def route_tool(ptx,langfuse_handler):
    
#     logger.info(f"Router received type: {ptx}")
#     tool_type=ptx.request.tool_type
#     LANGGRAPH_FUNCTION_MAP = {
#     'lesson_plan': lesson_plan_graph,
#     'rubric_generator': rubric_generator_langgraph,
#     'professional_email': professional_email_graph,
#     # 'text_proofreader': text_proofreader_langgraph,
#     'worksheet_generator': worksheet_generator_langgraph,
#     "basic_chatbot": basic_chatbot_langgraph,
# }

#     # Retrieve the target function object from the map using the assignment_type string
#     target_function = LANGGRAPH_FUNCTION_MAP.get(tool_type)
#     try:
#         result = target_function(ptx,langfuse_handler)
#         logger.info(f"Router finished processing type: {tool_type}")
#         return  result
#     except Exception as e:
#         logger.error(f"Error during execution of function for {tool_type}")
#         logger.error(f"Function: {getattr(target_function, '__name__', 'N/A')}")
#         logger.error(f"Error details: {e}")
#         raise
