from service.copilot_datamodels import Agent<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>elin<PERSON>,<PERSON><PERSON><PERSON>
import os
import requests
from service.agent_creator.prompts.create_agent import create_agent
from service.agent_creator.prompts.create_icons import build_icon_prompts_gpt,select_best_idea_gpt,brainstorm_icon_ideas_gpt
from service.agent_creator.prompts.create_tags import tagging
from service.gcp_services.firestore_database import Database
from service.gcp_services.gcp_bucket_service import GCPBucketServices
from service.openai_utils.gpt_call_new import openai_json_call,openai_image_call

def create_agent_pipeline(AgentContextPipeline:AgentContextPipeline):
    agent_id=AgentContextPipeline.agent_request.agent_id
    tool_name=AgentContextPipeline.agent_request.name
    description=AgentContextPipeline.agent_request.description
    input_params_dict=AgentContextPipeline.agent_request.input_params_dict
    output_desc=AgentContextPipeline.agent_request.output_desc
    owner=AgentContextPipeline.agent_request.owner
    env=AgentContextPipeline.env
    script_dir = os.path.dirname(os.path.abspath(__file__))        
    parent_dir = os.path.dirname(os.path.dirname(script_dir))      
    database_file_path = os.path.join(parent_dir, 'staging_config.json')
    db_object=Database(database_file_path)
    gcp_bucket_services=GCPBucketServices("uniqlearn-logging",config_path=database_file_path)
    print("Starting Agent Creation Pipeline")
    AgentContextPipeline.system_prompt,AgentContextPipeline.user_prompt=create_agent(name=tool_name,description=description,input_params_dict=input_params_dict,output_desc=output_desc)
    print(f"Agent {agent_id} created with system prompt: {AgentContextPipeline.system_prompt} and user prompt: {AgentContextPipeline.user_prompt}")
    ideas=brainstorm_icon_ideas_gpt(tool_name=tool_name,tool_desc=description)
    print(f"Agent {agent_id} brainstormed icon ideas: {ideas}")
    select_best_idea=select_best_idea_gpt(tool_name=tool_name,tool_desc=description,ideas=ideas)
    print(f"Agent {agent_id} selected best icon idea: {select_best_idea}")
    create_icon_prompts=build_icon_prompts_gpt(tool_name=tool_name,tool_desc=description,selected_idea=select_best_idea)
    print(f"Agent {agent_id} created icon prompts: {create_icon_prompts}")
    positive_prompt = create_icon_prompts['positive_prompt']
    negative_prompt = create_icon_prompts['negative_prompt']
    binary_image = openai_image_call(positive_prompt, negative_prompt)
    print(f"Agent {agent_id} created icon image: {binary_image}")
    image_file_path=f"{env}/FMS_Pipeline/{agent_id}/agent_icon.png"
    gcp_bucket_services.store_base64_image(binary_image,image_file_path)
    AgentContextPipeline.icon_path=image_file_path
    AgentContextPipeline.tags=tagging(tool_name=tool_name,tool_desc=description)
    print(f"Agent {agent_id} created with tags: {AgentContextPipeline.tags}")
    db_object.save_agent(agent_id,AgentContextPipeline.dict())
    return 
