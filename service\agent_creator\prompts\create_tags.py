from service.openai_utils.gpt_call_new import openai_text_call
def tagging(name, desc):

    prompt = (
        f"You are an expert product manager for educational AI tools. "
        f"Given the following tool name and description, return a concise list of up to 3 relevant, general tags or categories (no more than 2 words each), most suitable for organizing an educational app gallery. "
        f"Do not include brand names, only broad or useful categories. "
        f"Respond as a comma-separated list. "
        f"\n\nTool Name: {name}\nDescription: {desc}\n\nTags:"
    )
    tag_string = openai_text_call(prompt)
    # Clean/parse tags, making sure it's a list and not more than 3
    tags = [tag.strip() for tag in tag_string.split(",") if tag.strip()]
    return tags[:3]