# from google.cloud import storage

# import base64
# from pathlib import Path
# from typing import Union, Optional

# from google.cloud import storage
# from google.oauth2 import service_account


# class GCPBucketServices:
#     """
#     A tiny wrapper around Google Cloud Storage for uploading base-64 images.
#     """
#     def __init__(
#         self,
#         config_path: Union[str, Path] = "database_config.json",
#         bucket_name: str | None = None,
#     ):
#         """
#         Parameters
#         ----------
#         config_path : str | Path
#             Path to a Google service-account JSON key file.
#         bucket_name : str
#             Name of the target GCS bucket.
#         """
#         config_path = Path(config_path).expanduser().resolve()
#         if not config_path.exists():
#             raise FileNotFoundError(f"Cannot locate {config_path}")

#         if not bucket_name:
#             raise ValueError("bucket_name must be provided")

#         creds = service_account.Credentials.from_service_account_file(str(config_path))
#         self._client = storage.Client(credentials=creds, project=creds.project_id)
#         self._bucket = self._client.bucket(bucket_name)


#     def store_base64_image(self, image_b64: str, file_path: str) -> str:
#         """
#         Save a base-64–encoded image to GCS.

#         Parameters
#         ----------
#         image_b64 : str
#             The image encoded in base64.  
#             Both raw strings and *data URI* formats are accepted.
#         file_path : str
#             Destination object name inside the bucket (e.g. "images/avatar.png").

#         Returns
#         -------
#         str
#             The public URL of the uploaded object.
#         """
#         if not image_b64:
#             raise ValueError("image_b64 must not be empty")
#         if not file_path:
#             raise ValueError("file_path must not be empty")


#         content_type: Optional[str] = None
#         encoded_part = image_b64

#         if "," in image_b64 and image_b64.lstrip().startswith("data:"):
#             header, encoded_part = image_b64.split(",", 1)
#             try:
#                 content_type = header.split(";")[0].split(":")[1]
#             except IndexError:
#                 content_type = "application/octet-stream"

#         try:
#             binary_data = base64.b64decode(encoded_part, validate=True)
#         except (base64.binascii.Error, ValueError) as exc:
#             raise ValueError("Invalid base64 image input") from exc

#         blob = self._bucket.blob(file_path)
#         blob.upload_from_string(binary_data, content_type=content_type)

#         return blob.public_url
from google.cloud import storage
from google.oauth2 import service_account
from pathlib import Path
from typing import Union, Optional
import base64


class GCPBucketServices:
    def __init__(self,
                 bucket_name: str,
                 config_path: Union[str, Path, None] = None):
        """
        Parameters
        ----------
        bucket_name : str
            Target GCS bucket
        config_path : str | Path | None
            Optional path to a service-account JSON key.  If None, use ADC.
        """
        if not bucket_name:
            raise ValueError("bucket_name must be provided")

        if config_path:
            key_file = Path(config_path).expanduser().resolve()
            if not key_file.exists():
                raise FileNotFoundError(f"Cannot locate {key_file}")

            creds = service_account.Credentials.from_service_account_file(str(key_file))
            self._client = storage.Client(credentials=creds, project=creds.project_id)
        else:
            # Use Application Default Credentials (ADC)
            self._client = storage.Client()

        self._bucket = self._client.bucket(bucket_name)

    def store_base64_image(self, image_b64: str, file_path: str) -> str:
        # ... (same implementation as before)
        encoded_part = image_b64.split(",", 1)[-1]  # strips data URI if present
        binary_data = base64.b64decode(encoded_part, validate=True)

        blob = self._bucket.blob(file_path)
        blob.upload_from_string(binary_data)
        blob.make_public()
        return blob.public_url